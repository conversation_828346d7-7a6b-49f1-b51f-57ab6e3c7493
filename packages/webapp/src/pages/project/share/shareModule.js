import React, { useRef } from 'react';
import Router from 'next/router';
import Modal from 'sharedComponents/Modal/modal';
import GridIcon from 'svgpath/GridViewSvgPath';
import ListIcon from 'svgpath/ListViewSvgPath';
import InfoIcon from 'svgpath/NewInfoIconSvgPath';
import Icon from 'sharedComponents/Icon/Icon';
import ToolTip from 'sharedComponents/ToolTip/toolTip';
import AddNote from './addNote';
import GenerateCode from './generateCode';
import SnapsListView from './snapsListView';
import SnapsGridList from './snapGridView';
import EditOnMobile from './EditOnMobile';
import Style from './style/share.module.scss';
import SectionHeader from 'sharedComponents/SectionHeader/sectionHeader';

const ShareHistory = (props) => {
  const {
    showModal,
    snapInfoModalStatus,
    publishStatus,
    swapPublishStatus,
    unPublishStatus,
    viewPortStatus,
    setGridPortColour,
    setListPortColour,
    projectTitle,
    projectPreviewData,
    logLine,
    historyList,
    getHistory,
    setCreatorTags,
    setSnapInfoModalStatus,
    snapStepsStatus,
    currentFlow,
    shareProject,
    shareData,
    isGenerateQr,
    loadData,
    addSubmissionToCallout,
    callOutList,
    currentCallout,
    salesNewSnapRequest,
    updateSalesEstimation,
    closeModal,
    isGetShareStarted,
    handleViewPort,
    goBackToProject,
    handleChangePublishSnap,
    handleChangeGridPublishSnap,
    closeSnapStepsModal,
    setSnapSteps,
    handlePublishNewSnap,
    publishModalBody,
    closePublishModalStatus,
    isPublishProjectSnap,
    isSwapPublishSnap,
    isUnpublishProjectSnap,
  } = props;

  const infoRef = useRef(null);

  return (
    <>
      <div>
        <SectionHeader
          title="Manage Snapshots"
          description="View the different snapshots you’ve created, get the links
    and change which ones are public."
          showButton={false}
          customClass="waitListBtn"
          showBackButton={true}
          showMobileBackButton={false}
          handleBackBtn={() => {
            Router.back();
          }}
          descriptionCls="fs-14 mb-0"
        />
      </div>
      {/* ======= ALL MODALS (NO CHANGE) ======= */}
      <Modal
        modalShow={showModal}
        title="SNAPSHOT ALREADY EXISTS"
        body="Cannot take another snapshot without any changes"
        successCallback={() => closeModal && closeModal()}
        successBtnText="OK"
        titleClass={`${Style.modalTitle}`}
        isShowCrossBtn={false}
      />
      <Modal
        modalShow={snapInfoModalStatus}
        title="PUBLISH, view or share your project snapshots"
        body={
          <div className="ml-3 text-left text-primary">
            <p className="p2" style={{ lineHeight: '21px' }}>
              Create a version of your Project to share with others and choose
              who sees it. <br /> <br />
              You can publish your Snapshot on the SMASH platform to make it
              visible to Decision Makers, Creators, or both. Copy a link to
              share to share-off platform.
            </p>
          </div>
        }
        successCallback={() => isGetShareStarted && isGetShareStarted()}
        closeCallback={() => setSnapInfoModalStatus([1])}
        successBtnText="create new snapshot"
        titleClass={`${Style.modalPublishTitle}`}
        className={`${Style.modalContent} p-0`}
        isShowCrossBtn={true}
        modalSize="lg"
        successBtnClass="--primaryNavyShareModalLg"
        svgIconClass={`${Style.svgIconClass}`}
        modalFooter={Style.createButton}
      />
      <Modal
        modalShow={publishStatus}
        title="PUBLISH SNAPSHOT?"
        body={publishModalBody && publishModalBody()}
        className={`${Style.modalContent}`}
        closeCallback={() =>
          closePublishModalStatus && closePublishModalStatus()
        }
        closeBtnText="CANCEL"
        successCallback={() => isPublishProjectSnap && isPublishProjectSnap()}
        successBtnText="PUBLISH"
        modalSize="lg"
        titleClass={`${Style.modalPublishTitle}`}
        closeBtnClass="--secondaryChaney"
      />
      <Modal
        modalShow={swapPublishStatus}
        title="SWAP PUBLISHED SNAPSHOT?"
        body={publishModalBody && publishModalBody()}
        className={`${Style.modalContent}`}
        closeCallback={() =>
          closePublishModalStatus && closePublishModalStatus()
        }
        closeBtnText="CANCEL"
        successCallback={() => isSwapPublishSnap && isSwapPublishSnap()}
        successBtnText="SWAP"
        titleClass={`${Style.modalPublishTitle}`}
        closeBtnClass="--secondaryChaney"
        modalSize="lg"
      />
      <Modal
        modalShow={unPublishStatus}
        title="UNPUBLISH SNAPSHOT?"
        body="Are you sure you want to unpublish this snapshot? Commissioners, investors and other registered users on Smash will no longer be able to find your project in the Discover section."
        className={`${Style.modalContent}`}
        closeCallback={() =>
          closePublishModalStatus && closePublishModalStatus()
        }
        closeBtnText="CANCEL"
        successCallback={() =>
          isUnpublishProjectSnap && isUnpublishProjectSnap()
        }
        successBtnText="UNPUBLISH"
        titleClass={`${Style.modalPublishTitle}`}
        closeBtnClass="--secondaryNavyShareModalLg"
        successBtnClass="--primaryNavyShareModalLg"
        modalSize="lg"
        modalFooter={Style.saveButton}
        bodyClass={Style.modalDescription}
      />
      {/* ======= TITLE SECTION ======= */}
      <div className="container-fluid">
        <div className="row align-items-center justify-content-between mt-56">
          {/* PROJECT TITLE */}
          <div className="col-auto p-0 d-flex align-items-center">
            <h1 className="m-0">{projectTitle}</h1>
          </div>

          {/* GRID/LIST SWITCH */}
          <div className="col-auto p-0 d-flex align-items-center">
            <div
              className="p-2"
              onClick={() => handleViewPort && handleViewPort('list')}
              style={{ cursor: 'pointer' }}
            >
              <Icon iconSize="24px" color={setListPortColour} icon={ListIcon} />
            </div>
            <div
              className="pl-2 pt-2 pb-2"
              onClick={() => handleViewPort && handleViewPort('grid')}
              style={{ cursor: 'pointer' }}
            >
              <Icon iconSize="24px" color={setGridPortColour} icon={GridIcon} />
            </div>
          </div>
        </div>

        <EditOnMobile projectPreviewData={projectPreviewData} />

        {/* ======= LOG LINE ======= */}
        <div
          className={`${Style.logLineContainer} row justify-content-between p-20 d-none d-md-flex my-4`}
          style={viewPortStatus === 'grid' ? { display: 'none' } : {}}
        >
          <div className="align-content-center">
            <p className="text-white m-0">{logLine}</p>
          </div>
          <div>
            <button
              type="button"
              className={`${Style.editBtn} btn-light`}
              onClick={() => goBackToProject && goBackToProject()}
            >
              Edit Project
            </button>
          </div>
        </div>

        {/* ======= SNAPSHOTS HEADER ======= */}
        <div className="row justify-content-between">
          <div className="col-12 col-sm-12 col-md-4 p-0 d-flex flex-row">
            <h2>Snapshots</h2>
            <ToolTip
              clickIcon={
                <div ref={infoRef}>
                  <Icon
                    iconSize="20px"
                    color="#05012d"
                    icon={InfoIcon}
                    svgclass="ml-1 mb-2"
                    target={infoRef}
                  />
                </div>
              }
              position="bottom"
              contentClass="p2 text-left"
              content={
                <>
                  Publish your Project on SMASH <br /> <br />
                  When your Project Snapshot is Published, it will be viewable
                  by registered SMASH users... {/* (keeping same content) */}
                </>
              }
            />
          </div>

          <div className="col-auto p-0">
            <button
              type="button"
              className={`${Style.newSnapBtn} text-white`}
              onClick={() => isGetShareStarted && isGetShareStarted()}
            >
              Create Snapshot
            </button>
          </div>
        </div>
      </div>
      {/* ======= LIST / GRID VIEW ======= */}
      <div className="row mb-4 position-relative">
        {viewPortStatus === 'list' ? (
          projectPreviewData && (
            <div className="col-12 position-absolute">
              <SnapsListView
                snapsList={historyList}
                projectPreviewData={projectPreviewData}
                getHistory={getHistory}
                handleChangePublishSnap={handleChangePublishSnap}
                addSubmissionToCallout={addSubmissionToCallout}
                callout={callOutList[0] || currentCallout}
              />
            </div>
          )
        ) : (
          <div className="col-12 position-absolute">
            <SnapsGridList
              snapsList={historyList}
              projectPreviewData={projectPreviewData}
              getHistory={getHistory}
              handleChangePublishSnap={handleChangeGridPublishSnap}
              goBackToProject={goBackToProject}
              isGetShareStarted={isGetShareStarted}
              setCreatorTags={setCreatorTags}
            />
          </div>
        )}
      </div>
      {/* ======= ADD NOTE FLOW ======= */}
      {snapStepsStatus.addNote && (
        <div
          className={`${Style.getStartedContainer} col-11 p-0 position-absolute`}
        >
          <AddNote
            isGenerateQr={isGenerateQr}
            shareData={shareData}
            shareProject={shareProject}
            cancle={closeSnapStepsModal}
            snapStepsStatus={snapStepsStatus}
            setSnapSteps={setSnapSteps}
            currentFlow={currentFlow}
          />
        </div>
      )}
      {/* ======= GENERATE QR FLOW ======= */}
      {snapStepsStatus.generateQr && (
        <div
          className={`${Style.getStartedContainer} col-11 p-0 position-absolute`}
        >
          <GenerateCode
            cancle={closeSnapStepsModal}
            shareData={shareData}
            loadData={loadData}
            salesNewSnapRequest={salesNewSnapRequest}
            updateSalesEstimation={updateSalesEstimation}
            handleChangePublishSnap={handlePublishNewSnap}
            snapStepsStatus={snapStepsStatus}
            currentFlow={currentFlow}
            addSubmissionToCallout={addSubmissionToCallout}
            callout={callOutList[0] || currentCallout}
            setSnapSteps={setSnapSteps}
          />
        </div>
      )}
    </>
  );
};

export default ShareHistory;
