const version = `/v${process.env.DEFAULT_API_VERSION}`;
const routes = [
  {
    plugin: require('./health'),
    routes: {
      prefix: `${version}/health`,
    },
  },
  {
    plugin: require('./file'),
    routes: {
      prefix: `${version}`,
    },
  },
  {
    plugin: require('./user'),
    routes: {
      prefix: `${version}/user`,
    },
  },
  {
    plugin: require('./project'),
    routes: {
      prefix: `${version}/project`,
    },
  },
  {
    plugin: require('./getty'),
    routes: {
      prefix: `${version}/getty`,
    },
  },
  {
    plugin: require('./tag'),
    routes: {
      prefix: `${version}/tag`,
    },
  },
  {
    plugin: require('./collaborator'),
    routes: {
      prefix: `${version}/collaborator`,
    },
  },
  {
    plugin: require('./discovery'),
    routes: {
      prefix: `${version}/discovery`,
    },
  },
  {
    plugin: require('./callout'),
    routes: {
      prefix: `${version}/callout`,
    },
  },
  {
    plugin: require('./cron'),
    routes: {
      prefix: `${version}/cron`,
    },
  },
  {
    plugin: require('./payments'),
    routes: {
      prefix: `${version}/payments`,
    },
  },
  {
    plugin: require('./plan'),
    routes: {
      prefix: `${version}/product`,
    },
  },
  {
    plugin: require('./feature'),
    routes: {
      prefix: `${version}/feature`,
    },
  },
  {
    plugin: require('./subscription'),
    routes: {
      prefix: `${version}/subscription`,
    },
  },
  {
    plugin: require('./offer'),
    routes: {
      prefix: `${version}/offer`,
    },
  },
  {
    plugin: require('./script'),
    routes: {
      prefix: `${version}/script`,
    },
  },
  {
    plugin: require('./projectSnap'),
    routes: {
      prefix: `${version}/snap`,
    },
  },
  {
    plugin: require('./marketPlace'),
    routes: {
      prefix: `${version}/marketplace`,
    },
  },
  {
    plugin: require('./submission'),
    routes: {
      prefix: `${version}/submission`,
    },
  },
];

module.exports = routes;
