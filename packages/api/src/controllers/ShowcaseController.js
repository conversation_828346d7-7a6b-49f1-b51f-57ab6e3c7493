const Boom = require('@hapi/boom');
const { get } = require('lodash');
const ProjectPersonaService = require('../services/ProjectPersonaService');
const AuthService = require('../services/AuthService');

const projectPersonaService = new ProjectPersonaService();

class ShowcaseController {
  /**
   * Get complete user showcase data (merged from IM and Smash)
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response object
   */
  static async getUserShowcase(request, h) {
    try {
      const user = get(request, 'user', null);
      if (!user) {
        return h.response({
          statusCode: 404,
          message: 'User not found',
          data: {}
        }).code(404);
      }

      const userId = user._id;

      // Get select parameter from query (comma-separated list)
      const selectParam = request.query.select;
      const requestedFields = selectParam ? selectParam.split(',').map(f => f.trim()) : null;

      // Get user data from IM (Identity Manager)
      const jwtToken = request.headers.authorization?.replace('Bearer ', '');
      const userData = await AuthService.getUserProfileData(jwtToken, user.email);

      // Get project persona data from Smash DB
      const projectPersona = await projectPersonaService.getOrCreateProjectPersona(userId);

      // Get active project details if snapshot ID exists
      let activeProjectDetails = null;
      if (projectPersona.activeProject && projectPersona.activeProject.snapshotId) {
        activeProjectDetails = await projectPersonaService.getActiveProjectDetails(
          projectPersona.activeProject.snapshotId
        );
      }

      // Build complete showcase data
      const allData = {
        basicInfo: {
          id: userData._id,
          coverImage: get(userData, 'profile.profileImage', null),
          firstName: get(userData, 'profile.name.firstName', ''),
          lastName: get(userData, 'profile.name.lastName', ''),
          bio: get(userData, 'profile.bio', ''),
          location: get(userData, 'profile.city.address', ''),
          nationality: get(userData, 'profile.nationality', ''),
          ageRange: get(userData, 'profile.ageRange', '')
        },

        career: {
          primaryRole: get(userData, 'profile.primaryRole', ''),
          additionalRoles: get(userData, 'profile.additionalRoles', []),
          representation: get(userData, 'profile.representation', ''),
          careerStage: get(userData, 'profile.careerStage', ''),
          yearsOfExperience: get(userData, 'profile.yearsOfExperience', 0),
          numberOfPastProjects: get(userData, 'profile.numberOfPastProjects', 0),
          budgetRangeShorts: get(userData, 'profile.budgetRangeShorts', ''),
          budgetRangeFeatures: get(userData, 'profile.budgetRangeFeatures', '')
        },

        creative: {
          genres: get(userData, 'profile.creative.genres', []),
          formats: get(userData, 'profile.creative.formats', []),
          languages: get(userData, 'profile.creative.languages', []),
          audienceTypes: get(userData, 'profile.creative.audienceTypes', [])
        },

        links: {
          imdbLink: get(userData, 'profile.IMBDlink', ''),
          linkedIn: get(userData, 'profile.linkedIn', ''),
          website: get(userData, 'profile.website', '')
        },

        achievements: {
          awards: get(userData, 'profile.awards', []),
          training: get(userData, 'profile.training', []),
          memberships: get(userData, 'profile.memberships', [])
        },

        activeProject: activeProjectDetails,

        pastProjects: get(projectPersona, 'pastProjects', []).map(project => ({
          id: project._id,
          title: project.title,
          type: project.type,
          role: project.role,
          year: project.year,
          imdbLink: project.imdbLink,
          collaborators: project.collaborators,
          coverImage: project.coverImage
        })),

        preferences: {
          primaryGoals: get(userData, 'profile.preferences.primaryGoals', []),
          whoCanApproachYou: get(userData, 'profile.preferences.whoCanApproachYou', []),
          referralSource: get(userData, 'profile.preferences.referralSource', null)
        }
      };

      // Filter data based on requested fields
      let showcaseData = allData;
      if (requestedFields && requestedFields.length > 0) {
        showcaseData = {};
        requestedFields.forEach(field => {
          if (allData[field] !== undefined) {
            showcaseData[field] = allData[field];
          }
        });
      }

      return h.response({
        statusCode: 200,
        message: 'User showcase data retrieved successfully',
        data: showcaseData
      }).code(200);

    } catch (error) {
      request.logger.error(error, 'Error in ShowcaseController.getUserShowcase');
      throw new Boom.Boom(error, { statusCode: 500 });
    }
  }

  /**
   * Update user showcase data
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response object
   */
  static async updateUserShowcase(request, h) {
    try {
      const user = get(request, 'user', null);
      if (!user) {
        return h.response({
          statusCode: 404,
          message: 'User not found',
          data: {}
        }).code(404);
      }

      const userId = user._id;
      const payload = request.payload;

      // Update project persona data in Smash DB
      const updateData = {};

      if (payload.activeProject) {
        updateData.activeProject = payload.activeProject;
      }

      if (payload.pastProjects) {
        updateData.pastProjects = payload.pastProjects;
      }

      // Update project persona
      await projectPersonaService.updateProjectPersona(userId, updateData);

      return h.response({
        statusCode: 200,
        message: 'User showcase data updated successfully',
        data: { updated: true }
      }).code(200);

    } catch (error) {
      request.logger.error(error, 'Error in ShowcaseController.updateUserShowcase');
      throw new Boom.Boom(error, { statusCode: 500 });
    }
  }

  /**
   * Add a past project
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response object
   */
  static async addPastProject(request, h) {
    try {
      const user = get(request, 'user', null);
      if (!user) {
        return h.response({
          statusCode: 404,
          message: 'User not found',
          data: {}
        }).code(404);
      }

      const userId = user._id;
      const projectData = request.payload;

      // Add past project
      await projectPersonaService.addPastProject(userId, projectData);

      return h.response({
        statusCode: 200,
        message: 'Past project added successfully',
        data: { updated: true }
      }).code(200);

    } catch (error) {
      request.logger.error(error, 'Error in ShowcaseController.addPastProject');
      throw new Boom.Boom(error, { statusCode: 500 });
    }
  }

  /**
   * Update a specific past project
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response object
   */
  static async updatePastProject(request, h) {
    try {
      const user = get(request, 'user', null);
      if (!user) {
        return h.response({
          statusCode: 404,
          message: 'User not found',
          data: {}
        }).code(404);
      }

      const userId = user._id;
      const { projectId } = request.payload;
      const updateData = request.payload;

      // Update specific past project
      await projectPersonaService.updatePastProject(userId, projectId, updateData);

      return h.response({
        statusCode: 200,
        message: 'Past project updated successfully',
        data: { updated: true }
      }).code(200);

    } catch (error) {
      request.logger.error(error, 'Error in ShowcaseController.updatePastProject');
      throw new Boom.Boom(error, { statusCode: 500 });
    }
  }

  /**
   * Delete a specific past project
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response object
   */
  static async deletePastProject(request, h) {
    try {
      const user = get(request, 'user', null);
      if (!user) {
        return h.response({
          statusCode: 404,
          message: 'User not found',
          data: {}
        }).code(404);
      }

      const userId = user._id;
      const { projectId } = request.payload;

      // Delete specific past project
      await projectPersonaService.deletePastProject(userId, projectId);

      return h.response({
        statusCode: 200,
        message: 'Past project deleted successfully',
        data: { updated: true }
      }).code(200);

    } catch (error) {
      request.logger.error(error, 'Error in ShowcaseController.deletePastProject');
      throw new Boom.Boom(error, { statusCode: 500 });
    }
  }
}

module.exports = ShowcaseController;
